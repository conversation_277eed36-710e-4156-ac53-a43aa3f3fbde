from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordR<PERSON>quest<PERSON><PERSON>
from sqlalchemy.orm import Session
from datetime import timedelta

from app.models.base import get_db
from app.models.user import User
from app.schemas.auth import UserC<PERSON>, User<PERSON><PERSON>po<PERSON>, Token, User<PERSON>ogin
from app.core.security import get_password_hash, verify_password, create_access_token
from app.core.exceptions import Conflict<PERSON>rror, AuthenticationError, ValidationError
from app.core.config import settings
from app.core.dependencies import get_current_user

router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    """
    Register a new user.
    
    Args:
        user_data: User registration data
        db: Database session
        
    Returns:
        UserResponse: Created user information
        
    Raises:
        ConflictError: If username or email already exists
        ValidationError: If input data is invalid
    """
    # Check if username already exists
    existing_user = db.query(User).filter(
        (User.username == user_data.username) | (User.email == user_data.email)
    ).first()
    
    if existing_user:
        if existing_user.username == user_data.username:
            raise ConflictError("Username already registered")
        else:
            raise ConflictError("Email already registered")
    
    # Hash password
    hashed_password = get_password_hash(user_data.password)
    
    # Create new user
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        password_hash=hashed_password
    )
    
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    
    return UserResponse.from_orm(new_user)


@router.post("/login", response_model=Token)
async def login_user(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    Login user and return JWT token.
    
    Args:
        form_data: OAuth2 form data containing username and password
        db: Database session
        
    Returns:
        Token: JWT access token
        
    Raises:
        AuthenticationError: If credentials are invalid
    """
    # Find user by username or email
    user = db.query(User).filter(
        (User.username == form_data.username) | (User.email == form_data.username)
    ).first()
    
    if not user or not verify_password(form_data.password, user.password_hash):
        raise AuthenticationError("Invalid username or password")
    
    if not user.is_active:
        raise AuthenticationError("User account is disabled")
    
    # Create access token
    access_token_expires = timedelta(hours=settings.jwt_expire_hours)
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=int(access_token_expires.total_seconds())
    )


@router.post("/login-json", response_model=Token)
async def login_user_json(
    user_login: UserLogin,
    db: Session = Depends(get_db)
):
    """
    Login user with JSON payload (alternative to form data).
    
    Args:
        user_login: User login data
        db: Database session
        
    Returns:
        Token: JWT access token
        
    Raises:
        AuthenticationError: If credentials are invalid
    """
    # Find user by username or email
    user = db.query(User).filter(
        (User.username == user_login.username) | (User.email == user_login.username)
    ).first()
    
    if not user or not verify_password(user_login.password, user.password_hash):
        raise AuthenticationError("Invalid username or password")
    
    if not user.is_active:
        raise AuthenticationError("User account is disabled")
    
    # Create access token
    access_token_expires = timedelta(hours=settings.jwt_expire_hours)
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=int(access_token_expires.total_seconds())
    )


@router.get("/verify", response_model=UserResponse)
async def verify_token_and_get_user(
    current_user: User = Depends(get_current_user)
):
    """
    Verify JWT token and return user information.
    
    Args:
        current_user: The authenticated user, injected by dependency.
        
    Returns:
        UserResponse: User information corresponding to the token.
    """
    return current_user