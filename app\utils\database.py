from sqlalchemy import create_engine
from app.models.base import Base
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


def create_tables():
    """Create all database tables for development."""
    try:
        engine = create_engine(settings.database_url)
        
        # Import all models to ensure they're registered
        from app.models import user, session, message
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Failed to create database tables: {str(e)}")
        raise


def drop_tables():
    """Drop all database tables (use with caution)."""
    try:
        engine = create_engine(settings.database_url)
        
        # Import all models
        from app.models import user, session, message
        
        # Drop all tables
        Base.metadata.drop_all(bind=engine)
        logger.info("Database tables dropped successfully")
        
    except Exception as e:
        logger.error(f"Failed to drop database tables: {str(e)}")
        raise


if __name__ == "__main__":
    # Create tables when run as script
    create_tables()