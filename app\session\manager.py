import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from app.models.base import get_db
from app.models.user import User
from app.models.session import Session as SessionModel
from app.models.message import Message
from app.schemas.session import Session<PERSON><PERSON>, SessionResponse, SessionUpdate, SessionContext
from app.session.storage import session_storage
from app.agent.role_loader import role_loader
from app.core.config import settings
from app.core.exceptions import (
    SessionError, 
    NotFoundError, 
    ConflictError, 
    ValidationError,
    AuthorizationError
)


class SessionManager:
    """Manages user sessions with Redis caching and PostgreSQL persistence."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_session(
        self, 
        user_id: uuid.UUID, 
        session_data: SessionCreate
    ) -> SessionResponse:
        """
        Create a new session for a user.
        
        Args:
            user_id: User ID creating the session
            session_data: Session creation data
            
        Returns:
            SessionResponse: Created session information
            
        Raises:
            ValidationError: If role doesn't exist or user has too many sessions
            ConflictError: If session name already exists for user
        """
        # Validate role exists
        if not role_loader.role_exists(session_data.role_name):
            raise ValidationError(f"Role '{session_data.role_name}' does not exist")
        
        # Check if user has reached session limit
        user_session_count = self.db.query(SessionModel).filter(
            and_(
                SessionModel.user_id == user_id,
                SessionModel.is_active == True
            )
        ).count()
        
        if user_session_count >= settings.max_sessions_per_user:
            raise ValidationError(f"Maximum number of sessions ({settings.max_sessions_per_user}) reached")
        
        # Check if session name already exists for user
        existing_session = self.db.query(SessionModel).filter(
            and_(
                SessionModel.user_id == user_id,
                SessionModel.name == session_data.name,
                SessionModel.is_active == True
            )
        ).first()
        
        if existing_session:
            raise ConflictError(f"Session with name '{session_data.name}' already exists")
        
        # Generate unique session key
        session_key = f"sess_{uuid.uuid4().hex[:16]}"
        
        # Create session in database
        new_session = SessionModel(
            user_id=user_id,
            session_key=session_key,
            name=session_data.name,
            role_name=session_data.role_name,
            context={}
        )
        
        self.db.add(new_session)
        self.db.commit()
        self.db.refresh(new_session)
        
        # Initialize session context in Redis
        initial_context = {
            "role_name": session_data.role_name,
            "message_count": 0,
            "created_at": datetime.utcnow().isoformat(),
            "last_activity": datetime.utcnow().isoformat()
        }
        
        session_storage.save_session_context(session_key, initial_context)
        session_storage.update_session_metadata(session_key, {
            "session_id": str(new_session.id),
            "user_id": str(user_id),
            "role_name": session_data.role_name,
            "last_accessed": datetime.utcnow().isoformat()
        })
        
        return SessionResponse.from_orm(new_session)
    
    async def get_session(
        self, 
        session_key: str, 
        user_id: uuid.UUID
    ) -> Optional[SessionResponse]:
        """
        Get a session by key, ensuring user owns it.
        
        Args:
            session_key: Session key
            user_id: User ID requesting the session
            
        Returns:
            Optional[SessionResponse]: Session if found and owned by user
        """
        session = self.db.query(SessionModel).filter(
            and_(
                SessionModel.session_key == session_key,
                SessionModel.user_id == user_id,
                SessionModel.is_active == True
            )
        ).first()
        
        if not session:
            return None
        
        # Update last accessed time
        session.last_accessed = datetime.utcnow()
        self.db.commit()
        
        # Extend Redis TTL
        await session_storage.extend_session_ttl(session_key)
        
        return SessionResponse.from_orm(session)
    
    async def update_session(
        self, 
        session_key: str, 
        user_id: uuid.UUID, 
        update_data: SessionUpdate
    ) -> SessionResponse:
        """
        Update session information.
        
        Args:
            session_key: Session key
            user_id: User ID updating the session
            update_data: Update data
            
        Returns:
            SessionResponse: Updated session
            
        Raises:
            NotFoundError: If session not found
            ConflictError: If new name conflicts with existing session
        """
        session = self.db.query(SessionModel).filter(
            and_(
                SessionModel.session_key == session_key,
                SessionModel.user_id == user_id,
                SessionModel.is_active == True
            )
        ).first()
        
        if not session:
            raise NotFoundError("Session not found")
        
        # Check for name conflicts if updating name
        if update_data.name and update_data.name != session.name:
            existing_session = self.db.query(SessionModel).filter(
                and_(
                    SessionModel.user_id == user_id,
                    SessionModel.name == update_data.name,
                    SessionModel.is_active == True,
                    SessionModel.id != session.id
                )
            ).first()
            
            if existing_session:
                raise ConflictError(f"Session with name '{update_data.name}' already exists")
            
            session.name = update_data.name
        
        session.last_accessed = datetime.utcnow()
        self.db.commit()
        self.db.refresh(session)
        
        return SessionResponse.from_orm(session)
    
    async def delete_session(
        self, 
        session_key: str, 
        user_id: uuid.UUID
    ) -> bool:
        """
        Delete a session and all its data.
        
        Args:
            session_key: Session key
            user_id: User ID deleting the session
            
        Returns:
            bool: True if deleted successfully
            
        Raises:
            NotFoundError: If session not found
        """
        session = self.db.query(SessionModel).filter(
            and_(
                SessionModel.session_key == session_key,
                SessionModel.user_id == user_id,
                SessionModel.is_active == True
            )
        ).first()
        
        if not session:
            raise NotFoundError("Session not found")
        
        # Soft delete in database
        session.is_active = False
        self.db.commit()
        
        # Delete from Redis
        await session_storage.delete_session_data(session_key)
        
        return True
    
    async def list_user_sessions(
        self, 
        user_id: uuid.UUID,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        List all active sessions for a user.
        
        Args:
            user_id: User ID
            page: Page number (1-based)
            page_size: Number of sessions per page
            
        Returns:
            Dict containing sessions, total count, and pagination info
        """
        offset = (page - 1) * page_size
        
        # Get total count
        total = self.db.query(SessionModel).filter(
            and_(
                SessionModel.user_id == user_id,
                SessionModel.is_active == True
            )
        ).count()
        
        # Get sessions for current page
        sessions = self.db.query(SessionModel).filter(
            and_(
                SessionModel.user_id == user_id,
                SessionModel.is_active == True
            )
        ).order_by(desc(SessionModel.last_accessed)).offset(offset).limit(page_size).all()
        
        return {
            "sessions": [SessionResponse.from_orm(session) for session in sessions],
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
    
    async def get_session_context(self, session_key: str) -> Optional[SessionContext]:
        """
        Get session context from Redis with fallback to database.
        
        Args:
            session_key: Session key
            
        Returns:
            Optional[SessionContext]: Session context if found
        """
        # Try Redis first
        context = session_storage.get_session_context(session_key)
        
        if context:
            # Get session metadata from database
            session = self.db.query(SessionModel).filter(
                and_(
                    SessionModel.session_key == session_key,
                    SessionModel.is_active == True
                )
            ).first()
            
            if session:
                return SessionContext(
                    session_key=session_key,
                    role_name=session.role_name,
                    context=context,
                    last_accessed=session.last_accessed
                )
        
        return None
    
    async def update_session_activity(self, session_key: str) -> bool:
        """
        Update session last activity time.
        
        Args:
            session_key: Session key
            
        Returns:
            bool: True if updated successfully
        """
        # Update in Redis
        session_storage.update_session_metadata(session_key, {
            "last_activity": datetime.utcnow().isoformat()
        })
        
        # Update in database
        session = self.db.query(SessionModel).filter(
            and_(
                SessionModel.session_key == session_key,
                SessionModel.is_active == True
            )
        ).first()
        
        if session:
            session.last_accessed = datetime.utcnow()
            self.db.commit()
            return True
        
        return False


def get_session_manager(db: Session = get_db()) -> SessionManager:
    """Get session manager instance."""
    return SessionManager(db)