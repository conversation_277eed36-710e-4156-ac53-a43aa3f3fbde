import yaml
import os
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
from app.core.config import settings
from app.core.exceptions import ValidationError


class RoleConfig(BaseModel):
    """Role configuration model."""
    display_name: str
    description: str
    system_prompt: str
    tools: List[str]
    config: Dict[str, Any]
    is_active: bool = True


class RoleLoader:
    """Loads and manages AI role configurations from YAML files."""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or settings.roles_config_path
        self._roles: Dict[str, RoleConfig] = {}
        self._load_roles()
    
    def _load_roles(self):
        """Load roles from YAML configuration file."""
        try:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"Roles configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                roles_data = yaml.safe_load(file)
            
            if not roles_data:
                raise ValidationError("No roles found in configuration file")
            
            for role_name, role_data in roles_data.items():
                try:
                    self._roles[role_name] = RoleConfig(**role_data)
                except Exception as e:
                    raise ValidationError(f"Invalid configuration for role '{role_name}': {str(e)}")
                    
        except FileNotFoundError as e:
            raise ValidationError(f"Roles configuration file not found: {str(e)}")
        except yaml.YAMLError as e:
            raise ValidationError(f"Invalid YAML in roles configuration: {str(e)}")
        except Exception as e:
            raise ValidationError(f"Error loading roles configuration: {str(e)}")
    
    def get_role(self, role_name: str) -> Optional[RoleConfig]:
        """Get a specific role configuration."""
        return self._roles.get(role_name)
    
    def get_active_roles(self) -> Dict[str, RoleConfig]:
        """Get all active roles."""
        return {name: config for name, config in self._roles.items() if config.is_active}
    
    def list_role_names(self) -> List[str]:
        """Get list of all active role names."""
        return list(self.get_active_roles().keys())
    
    def role_exists(self, role_name: str) -> bool:
        """Check if a role exists and is active."""
        role = self._roles.get(role_name)
        return role is not None and role.is_active
    
    def reload_roles(self):
        """Reload roles from configuration file."""
        self._roles.clear()
        self._load_roles()
    
    def validate_role_tools(self, role_name: str, available_tools: List[str]) -> bool:
        """Validate that all tools required by a role are available."""
        role = self.get_role(role_name)
        if not role:
            return False
        
        return all(tool in available_tools for tool in role.tools)


# Global role loader instance
role_loader = RoleLoader()