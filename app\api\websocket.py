from fastapi import API<PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Query, Depends
from sqlalchemy.orm import Session
from typing import Dict, Optional
import json
import uuid
import asyncio
from datetime import datetime

from app.models.base import get_db
from app.models.user import User
from app.models.session import Session as SessionModel
from app.models.message import Message
from app.schemas.chat import StreamingMessage
from app.session.manager import SessionManager
from app.agent.factory import agent_factory
from app.core.security import verify_token
from app.core.exceptions import AuthenticationError, NotFoundError, LLMError
from app.core.config import settings

router = APIRouter()


class WebSocketManager:
    """Manages WebSocket connections for chat sessions."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, session_key: str):
        """Accept WebSocket connection and store it."""
        await websocket.accept()
        self.active_connections[session_key] = websocket
    
    def disconnect(self, session_key: str):
        """Remove WebSocket connection."""
        if session_key in self.active_connections:
            del self.active_connections[session_key]
    
    async def send_message(self, session_key: str, message: dict):
        """Send message to specific session WebSocket."""
        if session_key in self.active_connections:
            websocket = self.active_connections[session_key]
            try:
                await websocket.send_text(json.dumps(message))
            except Exception:
                # Connection might be closed, remove it
                self.disconnect(session_key)
    
    async def send_streaming_message(self, session_key: str, streaming_msg: StreamingMessage):
        """Send streaming message to WebSocket."""
        await self.send_message(session_key, streaming_msg.dict())


# Global WebSocket manager
websocket_manager = WebSocketManager()


async def get_user_from_token(token: str, db: Session) -> User:
    """Get user from JWT token for WebSocket authentication."""
    payload = verify_token(token)
    if not payload:
        raise AuthenticationError("Invalid token")
    
    user_id_str = payload.get("sub")
    if not user_id_str:
        raise AuthenticationError("Invalid token payload")
    
    try:
        user_id = uuid.UUID(user_id_str)
    except ValueError:
        raise AuthenticationError("Invalid user ID in token")
    
    user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
    if not user:
        raise AuthenticationError("User not found or inactive")
    
    return user


@router.websocket("/chat")
async def websocket_chat(
    websocket: WebSocket,
    session_key: str = Query(..., description="Session key"),
    token: str = Query(..., description="JWT authentication token")
):
    """
    WebSocket endpoint for real-time chat with streaming responses.
    
    Args:
        websocket: WebSocket connection
        session_key: Session key for the chat
        token: JWT authentication token
    """
    db = next(get_db())
    
    try:
        # Authenticate user
        user = await get_user_from_token(token, db)
        
        # Verify session exists and user owns it
        session_manager = SessionManager(db)
        session = await session_manager.get_session(session_key, user.id)
        if not session:
            await websocket.close(code=1008, reason="Session not found")
            return
        
        # Connect WebSocket
        await websocket_manager.connect(websocket, session_key)
        
        # Get or create AI agent
        agent = await agent_factory.create_agent(session_key, session.role_name)
        
        # Send connection success message
        await websocket_manager.send_streaming_message(session_key, StreamingMessage(
            type="connection.established",
            session_key=session_key,
            metadata={
                "role_name": session.role_name,
                "session_name": session.name
            }
        ))
        
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                if message_data.get("type") == "chat.message":
                    await handle_chat_message(
                        message_data,
                        session_key,
                        session,
                        agent,
                        user,
                        db
                    )
                elif message_data.get("type") == "ping":
                    # Respond to ping
                    await websocket_manager.send_streaming_message(session_key, StreamingMessage(
                        type="pong",
                        session_key=session_key
                    ))
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket_manager.send_streaming_message(session_key, StreamingMessage(
                    type="error",
                    session_key=session_key,
                    metadata={"error": "Invalid JSON message"}
                ))
            except Exception as e:
                await websocket_manager.send_streaming_message(session_key, StreamingMessage(
                    type="error",
                    session_key=session_key,
                    metadata={"error": str(e)}
                ))
    
    except AuthenticationError as e:
        await websocket.close(code=1008, reason=str(e))
    except Exception as e:
        await websocket.close(code=1011, reason=f"Server error: {str(e)}")
    finally:
        websocket_manager.disconnect(session_key)
        db.close()


async def handle_chat_message(
    message_data: dict,
    session_key: str,
    session: SessionModel,
    agent,
    user: User,
    db: Session
):
    """Handle incoming chat message and stream AI response."""
    try:
        user_message = message_data.get("content", "").strip()
        if not user_message:
            await websocket_manager.send_streaming_message(session_key, StreamingMessage(
                type="error",
                session_key=session_key,
                metadata={"error": "Empty message"}
            ))
            return
        
        # Generate unique message ID
        message_id = str(uuid.uuid4())
        
        # Send message start event
        await websocket_manager.send_streaming_message(session_key, StreamingMessage(
            type="chat.message.start",
            message_id=message_id,
            session_key=session_key
        ))
        
        # Get current message count for sequence numbers
        session_id = session.id  # session.id is already a UUID object
        message_count = db.query(Message).filter(
            Message.session_id == session_id
        ).count()
        
        # Save user message to database
        user_msg = Message.create_user_message(
            session_id=session_id,
            content=user_message,
            sequence_number=message_count + 1
        )
        db.add(user_msg)
        db.commit()
        
        # Generate streaming AI response
        full_response = ""
        
        async for chunk_content in agent.generate_streaming_response(user_message):
            if chunk_content:
                full_response += chunk_content
                
                # Send chunk to client
                await websocket_manager.send_streaming_message(session_key, StreamingMessage(
                    type="chat.message.chunk",
                    content=chunk_content,
                    message_id=message_id
                ))
        
        # Save AI response to database
        if full_response:
            ai_msg = Message.create_assistant_message(
                session_id=session_id,
                content=full_response,
                sequence_number=message_count + 2,
                metadata={
                    "role": session.role_name,
                    "model": settings.openai_model,
                    "streaming": True
                }
            )
            db.add(ai_msg)
            db.commit()
        
        # Send message end event
        await websocket_manager.send_streaming_message(session_key, StreamingMessage(
            type="chat.message.end",
            message_id=message_id,
            session_key=session_key,
            metadata={
                "role": session.role_name,
                "message_count": message_count + 2,
                "response_length": len(full_response)
            }
        ))
        
        # Update session activity
        session_manager = SessionManager(db)
        await session_manager.update_session_activity(session_key)
        
    except LLMError as e:
        await websocket_manager.send_streaming_message(session_key, StreamingMessage(
            type="error",
            session_key=session_key,
            metadata={
                "error": f"AI response error: {str(e)}",
                "error_type": "llm_error"
            }
        ))
    except Exception as e:
        db.rollback()
        await websocket_manager.send_streaming_message(session_key, StreamingMessage(
            type="error",
            session_key=session_key,
            metadata={
                "error": f"Server error: {str(e)}",
                "error_type": "server_error"
            }
        ))


@router.get("/connections")
async def get_active_connections():
    """Get information about active WebSocket connections (for debugging)."""
    return {
        "active_connections": len(websocket_manager.active_connections),
        "session_keys": list(websocket_manager.active_connections.keys())
    }