from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import uuid
import re


class SessionBase(BaseModel):
    """Base session schema."""
    name: str = Field(..., min_length=3, max_length=100, description="Session name")
    role_name: str = Field(..., description="AI role for this session")

    @validator('name')
    def validate_name(cls, v):
        """Validate session name constraints."""
        if not re.match(r'^[a-zA-Z0-9\s\-:._]{3,100}$', v):
            raise ValueError('Session name must be 3-100 characters, alphanumeric, spaces, hyphens, colons, dots, and underscores only')
        return v.strip()


class SessionCreate(SessionBase):
    """Schema for creating a new session."""
    pass


class SessionUpdate(BaseModel):
    """Schema for updating a session."""
    name: Optional[str] = Field(None, min_length=3, max_length=100, description="New session name")

    @validator('name')
    def validate_name(cls, v):
        """Validate session name constraints."""
        if v is not None:
            if not re.match(r'^[a-zA-Z0-9\s\-:._]{3,100}$', v):
                raise ValueError('Session name must be 3-100 characters, alphanumeric, spaces, hyphens, colons, dots, and underscores only')
            return v.strip()
        return v


class SessionResponse(SessionBase):
    """Schema for session response."""
    id: Union[str, uuid.UUID]
    user_id: Union[str, uuid.UUID]
    session_key: str
    context: Dict[str, Any]
    is_active: bool
    created_at: datetime
    last_accessed: datetime

    class Config:
        from_attributes = True
        # Automatically convert UUID to string
        json_encoders = {
            uuid.UUID: str
        }
        
    def model_dump(self, **kwargs):
        """Override to ensure UUIDs are always converted to strings."""
        data = super().model_dump(**kwargs)
        if isinstance(data.get('id'), uuid.UUID):
            data['id'] = str(data['id'])
        if isinstance(data.get('user_id'), uuid.UUID):
            data['user_id'] = str(data['user_id'])
        return data


class SessionListResponse(BaseModel):
    """Schema for session list response."""
    sessions: List[SessionResponse]
    total: int
    page: int
    page_size: int


class SessionContext(BaseModel):
    """Schema for session context data."""
    session_key: str
    role_name: str
    context: Dict[str, Any]
    last_accessed: datetime